#!/bin/bash

# Production-ready entrypoint script for Google App Engine Streamlit deployment

# Exit on any error
set -e

# Export all environment variables
export STREAMLIT_SERVER_HEADLESS=true
export STREAMLIT_SERVER_PORT=8081
export STREAMLIT_SERVER_ADDRESS=0.0.0.0
export STREAMLIT_SERVER_ENABLE_CORS=false
export STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
export STREAMLIT_SERVER_ENABLE_WEBSOCKET_COMPRESSION=false
export STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
export STREAMLIT_THEME_BASE=light
export STREAMLIT_THEME_PRIMARY_COLOR="#FF4B4B"
export STREAMLIT_THEME_BACKGROUND_COLOR="#FFFFFF"
export STREAMLIT_THEME_SECONDARY_BACKGROUND_COLOR="#F0F2F6"
export STREAMLIT_THEME_TEXT_COLOR="#262730"

# Set Python environment
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Log startup
echo "Starting Streamlit application on Google App Engine..."
echo "Port: $PORT"
echo "Server address: $STREAMLIT_SERVER_ADDRESS"

# Create necessary directories
mkdir -p ~/.streamlit

# Generate Streamlit config dynamically
cat > ~/.streamlit/config.toml << EOF
[server]
headless = true
port = 8081
address = "0.0.0.0"
enableCORS = false
enableXsrfProtection = false
enableWebsocketCompression = false
maxUploadSize = 200

[browser]
gatherUsageStats = false
serverAddress = "primeversion.rj.r.appspot.com"
serverPort = 443

[runner]
magicEnabled = true
installTracer = false
fixMatplotlib = true

[client]
caching = true
displayEnabled = true

[theme]
base = "light"
primaryColor = "#FF4B4B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
font = "sans serif"
EOF

# Log configuration
echo "Streamlit configuration created successfully"

# Start Streamlit with proper error handling
exec streamlit run op.py \
  --server.port=$PORT \
  --server.address=0.0.0.0 \
  --server.headless=true \
  --server.enableCORS=false \
  --server.enableXsrfProtection=false \
  --server.enableWebsocketCompression=false \
  --browser.serverAddress=primeversion.rj.r.appspot.com \
  --browser.serverPort=443 \
  --logger.level=warning 