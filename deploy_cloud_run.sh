#!/bin/bash

# 🚀 Deploy script for Google Cloud Run
# House MD PhD Application - NEXUS-MED System

echo "🩺 Iniciando deploy do House MD PhD no Google Cloud Run..."

# Configurações
PROJECT_ID="primeversion"
SERVICE_NAME="houseadvanced"
REGION="us-central1"
IMAGE_REGISTRY="gcr.io/supergordasdeferrari"
IMAGE_NAME="houseadvanced"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI não encontrado. Instale o Google Cloud SDK primeiro."
    exit 1
fi

# Verificar se docker está instalado
if ! command -v docker &> /dev/null; then
    log_error "Docker não encontrado. Instale o Docker primeiro."
    exit 1
fi

# Verificar se está logado no gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Não está logado no gcloud. Execute: gcloud auth login"
    exit 1
fi

# Configurar projeto
log_info "Configurando projeto: $PROJECT_ID"
gcloud config set project $PROJECT_ID

# Verificar se o arquivo Dockerfile existe
if [ ! -f "Dockerfile" ]; then
    log_error "Dockerfile não encontrado no diretório atual."
    exit 1
fi

# Verificar se o arquivo main.py ou op.py existe
if [ ! -f "op.py" ] && [ ! -f "main.py" ]; then
    log_error "Arquivo principal (op.py ou main.py) não encontrado."
    exit 1
fi

# Build da imagem Docker
log_info "Construindo imagem Docker: $FULL_IMAGE_NAME"
if docker build -t $FULL_IMAGE_NAME .; then
    log_success "Imagem Docker construída com sucesso!"
else
    log_error "Falha ao construir a imagem Docker."
    exit 1
fi

# Configurar autenticação do Docker para GCR
log_info "Configurando autenticação do Docker para GCR..."
gcloud auth configure-docker --quiet

# Push da imagem para GCR
log_info "Enviando imagem para Google Container Registry..."
if docker push $FULL_IMAGE_NAME; then
    log_success "Imagem enviada para GCR com sucesso!"
else
    log_error "Falha ao enviar imagem para GCR."
    exit 1
fi

# Deploy no Cloud Run
log_info "Fazendo deploy no Cloud Run..."
if gcloud run deploy $SERVICE_NAME \
    --image=$FULL_IMAGE_NAME \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --port=8501 \
    --memory=2Gi \
    --cpu=2 \
    --max-instances=10 \
    --timeout=3600 \
    --set-env-vars="PYTHONUNBUFFERED=1" \
    --quiet; then
    
    log_success "Deploy realizado com sucesso!"
    
    # Obter URL do serviço
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    
    echo ""
    echo "🎉 =================================="
    echo "✅ DEPLOY CONCLUÍDO COM SUCESSO!"
    echo "🎉 =================================="
    echo ""
    log_success "Serviço: $SERVICE_NAME"
    log_success "URL: $SERVICE_URL"
    log_success "Projeto: $PROJECT_ID"
    log_success "Região: $REGION"
    echo ""
    log_info "Aguarde alguns segundos para o serviço ficar totalmente disponível."
    echo ""
    
    # Testar se o serviço está respondendo
    log_info "Testando se o serviço está respondendo..."
    sleep 10
    
    if curl -s --head "$SERVICE_URL" | head -n 1 | grep -q "200 OK"; then
        log_success "Serviço está respondendo corretamente!"
        echo ""
        log_info "🌐 Acesse sua aplicação em: $SERVICE_URL"
    else
        log_warning "Serviço pode ainda estar inicializando. Tente acessar em alguns minutos."
    fi
    
else
    log_error "Falha no deploy do Cloud Run."
    exit 1
fi

echo ""
log_info "📋 Para fazer um novo deploy, execute novamente este script."
log_info "🔧 Para ver logs: gcloud run logs read --service=$SERVICE_NAME --region=$REGION"
log_info "📊 Para ver métricas: gcloud run services describe $SERVICE_NAME --region=$REGION"
echo ""
