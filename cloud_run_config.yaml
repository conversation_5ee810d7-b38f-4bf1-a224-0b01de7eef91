# Google Cloud Run Configuration for House MD PhD Application
# NEXUS-MED System - Medical AI Assistant

# ✅ CURRENT DEPLOYMENT INFO
deployment:
  service_name: "houseadvanced"
  project_id: "primeversion"
  region: "us-central1"
  url: "https://houseadvanced-*************.us-central1.run.app"
  revision: "houseadvanced-00001-n2d"
  status: "DEPLOYED"
  traffic: "100%"
  last_deployed: "2025-01-25"

# 🐳 CONTAINER CONFIGURATION
container:
  image_registry: "gcr.io/supergordasdeferrari"
  image_name: "houseadvanced"
  image_tag: "latest"
  port: 8501
  
# ⚙️ CLOUD RUN SETTINGS
cloud_run:
  platform: "managed"
  allow_unauthenticated: true
  memory: "2Gi"
  cpu: "2"
  max_instances: 10
  min_instances: 0
  timeout: "3600s"
  concurrency: 80

# 🔐 ENVIRONMENT VARIABLES
environment_variables:
  PYTHONUNBUFFERED: "1"
  # ANTHROPIC_API_KEY: "your-api-key-here" # Configure via gcloud secrets

# 🚀 DEPLOYMENT COMMANDS
commands:
  build_image: |
    docker build -t gcr.io/supergordasdeferrari/houseadvanced:latest .
  
  push_image: |
    docker push gcr.io/supergordasdeferrari/houseadvanced:latest
  
  deploy_service: |
    gcloud run deploy houseadvanced \
      --image=gcr.io/supergordasdeferrari/houseadvanced:latest \
      --platform=managed \
      --region=us-central1 \
      --allow-unauthenticated \
      --project=primeversion \
      --port=8501 \
      --memory=2Gi \
      --cpu=2 \
      --max-instances=10 \
      --timeout=3600
  
  quick_deploy: |
    ./deploy_cloud_run.sh
  
  view_logs: |
    gcloud run logs read --service=houseadvanced --region=us-central1
  
  describe_service: |
    gcloud run services describe houseadvanced --region=us-central1
  
  list_revisions: |
    gcloud run revisions list --service=houseadvanced --region=us-central1

# 🔧 TROUBLESHOOTING
troubleshooting:
  permission_error: |
    # If you get permission errors, run:
    gcloud projects add-iam-policy-binding supergordasdeferrari \
      --member="serviceAccount:<EMAIL>" \
      --role="roles/artifactregistry.reader"
  
  api_key_setup: |
    # To set up API key securely:
    echo "your-anthropic-api-key" | gcloud secrets create anthropic-api-key --data-file=-
    gcloud run services update houseadvanced \
      --update-env-vars ANTHROPIC_API_KEY="$(gcloud secrets versions access latest --secret=anthropic-api-key)"
  
  health_check: |
    # Check if service is healthy:
    curl -I https://houseadvanced-*************.us-central1.run.app

# 📊 MONITORING
monitoring:
  metrics_url: "https://console.cloud.google.com/run/detail/us-central1/houseadvanced/metrics"
  logs_url: "https://console.cloud.google.com/run/detail/us-central1/houseadvanced/logs"
  
# 🔄 ROLLBACK
rollback:
  list_revisions: |
    gcloud run revisions list --service=houseadvanced --region=us-central1
  
  rollback_to_previous: |
    # Replace REVISION_NAME with the desired revision
    gcloud run services update-traffic houseadvanced \
      --to-revisions=REVISION_NAME=100 \
      --region=us-central1

# 📝 NOTES
notes: |
  - Service is configured for medical AI assistance
  - Uses Streamlit framework on port 8501
  - Anthropic Claude API integration
  - Educational and diagnostic support tool
  - NOT for actual medical diagnosis
  - Always include medical disclaimers
