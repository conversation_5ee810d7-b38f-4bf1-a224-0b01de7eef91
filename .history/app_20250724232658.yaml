runtime: python39
instance_class: F2

env_variables:
  STREAMLIT_SERVER_HEADLESS: "true"
  STREAMLIT_SERVER_PORT: "8081"
  STREAMLIT_SERVER_ADDRESS: "0.0.0.0"
  STREAMLIT_SERVER_ENABLE_CORS: "false"
  STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION: "false"
  STREAMLIT_SERVER_ENABLE_WEBSOCKET_COMPRESSION: "false"
  STREAMLIT_BROWSER_SERVER_ADDRESS: "primeversion.rj.r.appspot.com"
  STREAMLIT_BROWSER_SERVER_PORT: "443"
  PYTHONUNBUFFERED: "1"
  OAUTH_CLIENT_ID: "${sm://OAUTH_CLIENT_ID}"
  OAUTH_CLIENT_SECRET: "${sm://OAUTH_CLIENT_SECRET}"
  ANTHROPIC_API_KEY: "${sm://ANTHROPIC_API_KEY}"

entrypoint: ./entrypoint.sh

automatic_scaling:
  target_cpu_utilization: 0.65
  min_instances: 1
  max_instances: 10
  min_pending_latency: 30ms
  max_pending_latency: automatic
  max_concurrent_requests: 80

handlers:
- url: /.*
  script: auto
  secure: always

inbound_services:
- warmup

includes:
- env_variables.yaml
- secrets.yaml