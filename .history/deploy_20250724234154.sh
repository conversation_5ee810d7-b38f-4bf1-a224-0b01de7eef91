#!/bin/bash

# Deploy script for App Engine

echo "🚀 Deploying to App Engine..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI not found. Please install it first."
    exit 1
fi

# Set project (update with your project ID)
PROJECT_ID="primeversion"
echo "📋 Using project: $PROJECT_ID"

# Set the project
gcloud config set project $PROJECT_ID

# Deploy the application
echo "🔧 Deploying application..."
gcloud app deploy app.yaml \
    --quiet \
    --promote \
    --stop-previous-version

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "🌐 Your app is available at: https://$PROJECT_ID.rj.r.appspot.com"
    
    # Check logs for any issues
    echo "📊 Checking recent logs..."
    gcloud app logs read --limit=20
else
    echo "❌ Deployment failed. Check the error messages above."
    exit 1
fi