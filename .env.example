# House MD PhD - Environment Variables Configuration
# Copy this file to .env and fill in your actual values

# 🔑 API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 🌐 Proxy Settings (if needed)
# HTTP_PROXY=http://proxy.company.com:8080
# HTTPS_PROXY=https://proxy.company.com:8080

# ☁️ Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=primeversion
GOOGLE_CLOUD_REGION=us-central1

# 🚀 Cloud Run Service Configuration
K_SERVICE=houseadvanced
K_REVISION=houseadvanced-00001-n2d
K_CONFIGURATION=houseadvanced

# 🐳 Container Configuration
PORT=8501

# 🔧 Application Settings
PYTHONUNBUFFERED=1
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0

# 📊 Monitoring and Logging
LOG_LEVEL=INFO

# 🏥 Medical Application Settings
APP_NAME="House MD PhD"
APP_VERSION="1.0.0"
MEDICAL_DISCLAIMER_ENABLED=true

# 📝 Notes:
# - Never commit the actual .env file with real API keys
# - Use Google Cloud Secret Manager for production API keys
# - For Cloud Run, set environment variables in the service configuration
# - Local development: copy this to .env and fill in your values
