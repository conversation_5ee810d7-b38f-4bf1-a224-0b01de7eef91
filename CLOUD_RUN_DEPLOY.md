# 🚀 House MD PhD - Google Cloud Run Deployment Guide

## ✅ Current Deployment Status

**🎉 Successfully Deployed!**

- **Service Name:** `houseadvanced`
- **URL:** https://houseadvanced-*************.us-central1.run.app
- **Project:** `primeversion`
- **Region:** `us-central1`
- **Revision:** `houseadvanced-00001-n2d`
- **Traffic:** 100% to current revision
- **Status:** ✅ ACTIVE

## 🔄 Quick Redeploy

### Option 1: Automated Script (Recommended)
```bash
./deploy_cloud_run.sh
```

### Option 2: Manual Commands
```bash
# 1. Build and push image
docker build -t gcr.io/supergordasdeferrari/houseadvanced:latest .
docker push gcr.io/supergordasdeferrari/houseadvanced:latest

# 2. Deploy to Cloud Run
gcloud run deploy houseadvanced \
  --image=gcr.io/supergordasdeferrari/houseadvanced:latest \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --project=primeversion \
  --port=8501 \
  --memory=2Gi \
  --cpu=2 \
  --max-instances=10 \
  --timeout=3600
```

## 🔧 Prerequisites

1. **Google Cloud SDK installed and authenticated**
   ```bash
   gcloud auth login
   gcloud config set project primeversion
   ```

2. **Docker installed and running**

3. **Required permissions** (already configured):
   - `roles/artifactregistry.reader` on `supergordasdeferrari` project
   - `roles/storage.objectViewer` on `supergordasdeferrari` project

## 📊 Monitoring & Management

### View Logs
```bash
gcloud run logs read --service=houseadvanced --region=us-central1 --limit=50
```

### Service Status
```bash
gcloud run services describe houseadvanced --region=us-central1
```

### List Revisions
```bash
gcloud run revisions list --service=houseadvanced --region=us-central1
```

### Traffic Management
```bash
# Split traffic between revisions
gcloud run services update-traffic houseadvanced \
  --to-revisions=REVISION-1=50,REVISION-2=50 \
  --region=us-central1
```

## 🔐 Environment Variables

### Current Configuration
- `PYTHONUNBUFFERED=1` (for proper logging)
- `ANTHROPIC_API_KEY` (configure via secrets)

### Setting API Key Securely
```bash
# Create secret
echo "your-anthropic-api-key" | gcloud secrets create anthropic-api-key --data-file=-

# Update service with secret
gcloud run services update houseadvanced \
  --update-env-vars ANTHROPIC_API_KEY="$(gcloud secrets versions access latest --secret=anthropic-api-key)" \
  --region=us-central1
```

## 🚨 Troubleshooting

### Permission Issues
If you encounter permission errors:
```bash
gcloud projects add-iam-policy-binding supergordasdeferrari \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/artifactregistry.reader"
```

### Service Not Responding
1. Check logs: `gcloud run logs read --service=houseadvanced --region=us-central1`
2. Verify image: `docker run -p 8501:8501 gcr.io/supergordasdeferrari/houseadvanced:latest`
3. Check health: `curl -I https://houseadvanced-*************.us-central1.run.app`

### Build Issues
1. Ensure Dockerfile is present
2. Check if all dependencies are in requirements.txt
3. Verify Python version compatibility

## 🔄 Rollback Procedure

### List Available Revisions
```bash
gcloud run revisions list --service=houseadvanced --region=us-central1
```

### Rollback to Previous Revision
```bash
gcloud run services update-traffic houseadvanced \
  --to-revisions=PREVIOUS_REVISION_NAME=100 \
  --region=us-central1
```

## 📈 Performance Configuration

### Current Settings
- **Memory:** 2Gi
- **CPU:** 2 vCPU
- **Max Instances:** 10
- **Timeout:** 3600s (1 hour)
- **Concurrency:** 80 requests per instance

### Scaling Adjustments
```bash
# Increase memory and CPU for better performance
gcloud run services update houseadvanced \
  --memory=4Gi \
  --cpu=4 \
  --region=us-central1

# Adjust instance limits
gcloud run services update houseadvanced \
  --max-instances=20 \
  --min-instances=1 \
  --region=us-central1
```

## 🌐 Custom Domain (Optional)

### Map Custom Domain
```bash
gcloud run domain-mappings create \
  --service=houseadvanced \
  --domain=your-domain.com \
  --region=us-central1
```

## 📝 Important Notes

- **Medical Disclaimer:** This application is for educational purposes only
- **API Costs:** Monitor Anthropic API usage and costs
- **Security:** Never commit API keys to version control
- **Compliance:** Ensure HIPAA compliance if handling real medical data
- **Monitoring:** Set up alerts for service health and costs

## 🆘 Support

For issues with:
- **Cloud Run:** Check Google Cloud Console or contact Google Support
- **Application:** Review application logs and Streamlit documentation
- **API:** Check Anthropic API status and documentation

---

**Last Updated:** 2025-01-25  
**Deployment Version:** houseadvanced-00001-n2d  
**Maintainer:** House Project Team
